import {CustomNode as CustomNodeType, CustomEdge} from '../components/Nodes/types';
import {
    measurePerformance,
    layoutCache,
    performanceMonitor,
    optimizeEdges,
    optimizeMemoryUsage,
    getOptimalBatchSize
} from './layoutPerformance';

export enum LayoutType {
    HIERARCHICAL = 'hierarchical',
    GRID = 'grid',
    FORCE = 'force',
    CIRCULAR = 'circular',
    TREE = 'tree',
    COMPACT = 'compact'
}

interface LayoutOptions {
    nodeSpacing: number;
    levelSpacing: number;
    direction: 'TB' | 'LR' | 'BT' | 'RL';
    padding?: number;
    animate?: boolean;
}

const DEFAULT_OPTIONS: LayoutOptions = {
    nodeSpacing: 150,
    levelSpacing: 200,
    direction: 'TB',
    padding: 50,
    animate: true
};

/**
 * 优化的层次化布局算法
 * 基于节点的连接关系进行分层排列，支持更好的性能和视觉效果
 */
function hierarchicalLayout(nodes: CustomNodeType[], edges: CustomEdge[], options: LayoutOptions): CustomNodeType[] {
    if (nodes.length === 0) return nodes;

    console.log('开始优化层次布局，节点数量:', nodes.length, '边数量:', edges.length, '选项:', options);

    // 构建邻接表和反向邻接表
    const adjacencyList = new Map<string, string[]>();
    const reverseAdjacencyList = new Map<string, string[]>();
    const inDegree = new Map<string, number>();
    const outDegree = new Map<string, number>();

    // 初始化
    nodes.forEach(node => {
        adjacencyList.set(node.id, []);
        reverseAdjacencyList.set(node.id, []);
        inDegree.set(node.id, 0);
        outDegree.set(node.id, 0);
    });

    // 构建图结构
    edges.forEach(edge => {
        const source = edge.source;
        const target = edge.target;

        if (adjacencyList.has(source) && inDegree.has(target)) {
            adjacencyList.get(source)!.push(target);
            reverseAdjacencyList.get(target)!.push(source);
            inDegree.set(target, (inDegree.get(target) || 0) + 1);
            outDegree.set(source, (outDegree.get(source) || 0) + 1);
        }
    });

    // 优化的拓扑排序分层算法
    const levels: string[][] = [];
    const queue: string[] = [];
    const visited = new Set<string>();
    const levelMap = new Map<string, number>();

    // 找到所有根节点（入度为0）
    const rootNodes = nodes.filter(node => inDegree.get(node.id) === 0);

    // 如果没有根节点，找到最重要的节点作为起始点
    if (rootNodes.length === 0 && nodes.length > 0) {
        // 选择出度最大的节点作为根节点
        const maxOutDegreeNode = nodes.reduce((max, node) =>
            (outDegree.get(node.id) || 0) > (outDegree.get(max.id) || 0) ? node : max
        );
        rootNodes.push(maxOutDegreeNode);
    }

    // 初始化队列
    rootNodes.forEach(node => {
        queue.push(node.id);
        levelMap.set(node.id, 0);
    });

    // 改进的分层处理 - 使用BFS确保层次清晰
    while (queue.length > 0) {
        const currentLevel: string[] = [];
        const currentLevelNumber = Math.min(...queue.map(id => levelMap.get(id) || 0));

        // 处理当前层级的所有节点
        const currentLevelNodes = queue.filter(id => levelMap.get(id) === currentLevelNumber);
        queue.splice(0, queue.length, ...queue.filter(id => levelMap.get(id) !== currentLevelNumber));

        currentLevelNodes.forEach(nodeId => {
            if (!visited.has(nodeId)) {
                visited.add(nodeId);
                currentLevel.push(nodeId);

                // 将子节点加入下一层
                const children = adjacencyList.get(nodeId) || [];
                children.forEach(childId => {
                    if (!visited.has(childId)) {
                        const childLevel = currentLevelNumber + 1;
                        const existingLevel = levelMap.get(childId);

                        // 确保子节点在正确的层级
                        if (existingLevel === undefined || existingLevel < childLevel) {
                            levelMap.set(childId, childLevel);
                            if (!queue.includes(childId)) {
                                queue.push(childId);
                            }
                        }
                    }
                });
            }
        });

        if (currentLevel.length > 0) {
            levels.push(currentLevel);
        }
    }

    // 处理孤立节点
    const isolatedNodes = nodes.filter(node => !visited.has(node.id));
    if (isolatedNodes.length > 0) {
        levels.push(isolatedNodes.map(node => node.id));
    }

    console.log('优化分层结果:', levels, '层级映射:', Object.fromEntries(levelMap));

    // 优化的位置计算
    const newNodes = nodes.map(node => ({ ...node }));
    const nodeMap = new Map(newNodes.map(node => [node.id, node]));

    // 计算布局参数
    const totalLevels = levels.length;
    const maxNodesInLevel = Math.max(...levels.map(level => level.length));
    const padding = options.padding || 50;

    // 为每层节点计算最优位置
    levels.forEach((level, levelIndex) => {
        // 对每层内的节点进行排序，优化视觉效果
        const sortedLevel = [...level].sort((a, b) => {
            const nodeA = nodes.find(n => n.id === a);
            const nodeB = nodes.find(n => n.id === b);
            // 按节点类型或其他属性排序，提高布局美观度
            return (nodeA?.data?.type || '').localeCompare(nodeB?.data?.type || '');
        });

        sortedLevel.forEach((nodeId, nodeIndex) => {
            const node = nodeMap.get(nodeId);
            if (node) {
                if (options.direction === 'TB' || options.direction === 'BT') {
                    // 垂直布局 - 优化间距
                    const levelWidth = (sortedLevel.length - 1) * options.nodeSpacing;
                    const startX = -levelWidth / 2;
                    const yPosition = options.direction === 'TB'
                        ? levelIndex * options.levelSpacing + padding
                        : -(levelIndex * options.levelSpacing + padding);

                    node.position = {
                        x: startX + nodeIndex * options.nodeSpacing,
                        y: yPosition
                    };
                } else {
                    // 水平布局 - 优化从左到右的排列
                    const levelHeight = (sortedLevel.length - 1) * options.nodeSpacing;
                    const startY = -levelHeight / 2;
                    const xPosition = options.direction === 'LR'
                        ? levelIndex * options.levelSpacing + padding
                        : -(levelIndex * options.levelSpacing + padding);

                    node.position = {
                        x: xPosition,
                        y: startY + nodeIndex * options.nodeSpacing
                    };
                }
            }
        });
    });

    console.log('优化位置计算完成:', newNodes.map(n => ({ id: n.id, position: n.position })));
    return newNodes;
}

/**
 * 优化的网格布局算法
 */
function gridLayout(nodes: CustomNodeType[], options: LayoutOptions): CustomNodeType[] {
    if (nodes.length === 0) return nodes;

    // 计算最优的行列比例
    const aspectRatio = 16 / 9; // 黄金比例的近似值
    const cols = Math.ceil(Math.sqrt(nodes.length * aspectRatio));
    const rows = Math.ceil(nodes.length / cols);
    const padding = options.padding || 50;

    // 计算居中偏移
    const totalWidth = (cols - 1) * options.nodeSpacing;
    const totalHeight = (rows - 1) * options.nodeSpacing;
    const offsetX = -totalWidth / 2;
    const offsetY = -totalHeight / 2;

    return nodes.map((node, index) => {
        const row = Math.floor(index / cols);
        const col = index % cols;

        return {
            ...node,
            position: {
                x: offsetX + col * options.nodeSpacing,
                y: offsetY + row * options.nodeSpacing
            }
        };
    });
}

/**
 * 紧凑布局算法 - 最小化节点间距离
 */
function compactLayout(nodes: CustomNodeType[], edges: CustomEdge[], options: LayoutOptions): CustomNodeType[] {
    if (nodes.length === 0) return nodes;

    // 先使用层次布局获得基本结构
    const hierarchicalNodes = hierarchicalLayout(nodes, edges, {
        ...options,
        nodeSpacing: options.nodeSpacing * 0.8, // 减少间距
        levelSpacing: options.levelSpacing * 0.7
    });

    // 进一步优化位置，减少空白区域
    return hierarchicalNodes.map(node => ({
        ...node,
        position: {
            x: node.position.x * 0.9,
            y: node.position.y * 0.9
        }
    }));
}

/**
 * 树形布局算法 - 专门用于树状结构
 */
function treeLayout(nodes: CustomNodeType[], edges: CustomEdge[], options: LayoutOptions): CustomNodeType[] {
    if (nodes.length === 0) return nodes;

    // 找到根节点
    const inDegree = new Map<string, number>();
    nodes.forEach(node => inDegree.set(node.id, 0));
    edges.forEach(edge => {
        inDegree.set(edge.target, (inDegree.get(edge.target) || 0) + 1);
    });

    const rootNodes = nodes.filter(node => inDegree.get(node.id) === 0);
    if (rootNodes.length === 0) return hierarchicalLayout(nodes, edges, options);

    // 构建树结构
    const children = new Map<string, string[]>();
    nodes.forEach(node => children.set(node.id, []));
    edges.forEach(edge => {
        children.get(edge.source)?.push(edge.target);
    });

    const newNodes = nodes.map(node => ({ ...node }));
    const nodeMap = new Map(newNodes.map(node => [node.id, node]));
    const positioned = new Set<string>();

    // 递归布局函数
    function layoutSubtree(nodeId: string, x: number, y: number, level: number): number {
        const node = nodeMap.get(nodeId);
        if (!node || positioned.has(nodeId)) return x;

        positioned.add(nodeId);
        node.position = { x, y };

        const nodeChildren = children.get(nodeId) || [];
        if (nodeChildren.length === 0) return x + options.nodeSpacing;

        let currentX = x - ((nodeChildren.length - 1) * options.nodeSpacing) / 2;
        nodeChildren.forEach(childId => {
            currentX = layoutSubtree(childId, currentX, y + options.levelSpacing, level + 1);
        });

        return x + options.nodeSpacing;
    }

    // 布局每个根节点
    let rootX = 0;
    rootNodes.forEach(rootNode => {
        layoutSubtree(rootNode.id, rootX, 0, 0);
        rootX += options.levelSpacing * 2;
    });

    return newNodes;
}

/**
 * 圆形布局算法
 */
function circularLayout(nodes: CustomNodeType[], options: LayoutOptions): CustomNodeType[] {
    if (nodes.length === 0) return nodes;
    if (nodes.length === 1) {
        return [{
            ...nodes[0],
            position: { x: 0, y: 0 }
        }];
    }

    const radius = Math.max(200, nodes.length * 30);
    const angleStep = (2 * Math.PI) / nodes.length;

    return nodes.map((node, index) => {
        const angle = index * angleStep;
        return {
            ...node,
            position: {
                x: Math.cos(angle) * radius,
                y: Math.sin(angle) * radius
            }
        };
    });
}

/**
 * 力导向布局算法（简化版）
 */
function forceLayout(nodes: CustomNodeType[], edges: CustomEdge[], options: LayoutOptions): CustomNodeType[] {
    if (nodes.length === 0) return nodes;

    const newNodes = nodes.map(node => ({ ...node }));
    const iterations = 50;
    const repulsionStrength = 1000;
    const attractionStrength = 0.1;
    const damping = 0.9;

    // 初始化速度
    const velocities = new Map<string, {x: number, y: number}>();
    newNodes.forEach(node => {
        velocities.set(node.id, {x: 0, y: 0});
    });

    for (let iter = 0; iter < iterations; iter++) {
        // 计算斥力
        for (let i = 0; i < newNodes.length; i++) {
            for (let j = i + 1; j < newNodes.length; j++) {
                const node1 = newNodes[i];
                const node2 = newNodes[j];

                const dx = node1.position.x - node2.position.x;
                const dy = node1.position.y - node2.position.y;
                const distance = Math.sqrt(dx * dx + dy * dy) || 1;

                const force = repulsionStrength / (distance * distance);
                const fx = (dx / distance) * force;
                const fy = (dy / distance) * force;

                const vel1 = velocities.get(node1.id)!;
                const vel2 = velocities.get(node2.id)!;

                vel1.x += fx;
                vel1.y += fy;
                vel2.x -= fx;
                vel2.y -= fy;
            }
        }

        // 计算引力（基于边连接）
        edges.forEach(edge => {
            const sourceNode = newNodes.find(n => n.id === edge.source);
            const targetNode = newNodes.find(n => n.id === edge.target);

            if (sourceNode && targetNode) {
                const dx = targetNode.position.x - sourceNode.position.x;
                const dy = targetNode.position.y - sourceNode.position.y;
                const distance = Math.sqrt(dx * dx + dy * dy) || 1;

                const force = attractionStrength * distance;
                const fx = (dx / distance) * force;
                const fy = (dy / distance) * force;

                const sourceVel = velocities.get(sourceNode.id)!;
                const targetVel = velocities.get(targetNode.id)!;

                sourceVel.x += fx;
                sourceVel.y += fy;
                targetVel.x -= fx;
                targetVel.y -= fy;
            }
        });

        // 更新位置
        newNodes.forEach(node => {
            const vel = velocities.get(node.id)!;
            node.position.x += vel.x;
            node.position.y += vel.y;

            // 应用阻尼
            vel.x *= damping;
            vel.y *= damping;
        });
    }

    return newNodes;
}

/**
 * 智能布局选择 - 根据图的特征自动选择最佳布局
 */
export function smartLayout(nodes: CustomNodeType[], edges: CustomEdge[], options?: Partial<LayoutOptions>): CustomNodeType[] {
    if (nodes.length === 0) return nodes;

    const mergedOptions = { ...DEFAULT_OPTIONS, ...options };

    // 分析图的特征
    const nodeCount = nodes.length;
    const edgeCount = edges.length;
    const density = edgeCount / (nodeCount * (nodeCount - 1) / 2);

    // 检查是否为树状结构
    const isTree = edgeCount === nodeCount - 1;

    // 检查是否有明显的层次结构
    const inDegree = new Map<string, number>();
    nodes.forEach(node => inDegree.set(node.id, 0));
    edges.forEach(edge => {
        inDegree.set(edge.target, (inDegree.get(edge.target) || 0) + 1);
    });
    const rootNodes = nodes.filter(node => inDegree.get(node.id) === 0);
    const hasHierarchy = rootNodes.length > 0 && rootNodes.length < nodeCount * 0.5;

    console.log('图分析结果:', { nodeCount, edgeCount, density, isTree, hasHierarchy, rootNodes: rootNodes.length });

    // 智能选择布局算法
    if (nodeCount <= 3) {
        return circularLayout(nodes, mergedOptions);
    } else if (isTree) {
        return treeLayout(nodes, edges, mergedOptions);
    } else if (hasHierarchy) {
        return hierarchicalLayout(nodes, edges, mergedOptions);
    } else if (density > 0.3) {
        return forceLayout(nodes, edges, mergedOptions);
    } else if (nodeCount <= 20) {
        return compactLayout(nodes, edges, mergedOptions);
    } else {
        return gridLayout(nodes, mergedOptions);
    }
}

/**
 * 优化的自动布局主函数
 */
export function autoLayout(
    nodes: CustomNodeType[],
    edges: CustomEdge[],
    layoutType: LayoutType = LayoutType.HIERARCHICAL,
    customOptions?: Partial<LayoutOptions>
): CustomNodeType[] {
    const options = { ...DEFAULT_OPTIONS, ...customOptions };

    console.log('执行布局算法:', layoutType, '选项:', options);

    switch (layoutType) {
        case LayoutType.HIERARCHICAL:
            return hierarchicalLayout(nodes, edges, options);
        case LayoutType.GRID:
            return gridLayout(nodes, options);
        case LayoutType.CIRCULAR:
            return circularLayout(nodes, options);
        case LayoutType.FORCE:
            return forceLayout(nodes, edges, options);
        case LayoutType.TREE:
            return treeLayout(nodes, edges, options);
        case LayoutType.COMPACT:
            return compactLayout(nodes, edges, options);
        default:
            return smartLayout(nodes, edges, options);
    }
}

/**
 * 优化的居中布局 - 将所有节点移动到画布中心，支持边距设置
 */
export function centerLayout(nodes: CustomNodeType[], padding: number = 50): CustomNodeType[] {
    if (nodes.length === 0) return nodes;

    // 计算所有节点的边界框
    const positions = nodes.map(n => n.position);
    const minX = Math.min(...positions.map(p => p.x));
    const maxX = Math.max(...positions.map(p => p.x));
    const minY = Math.min(...positions.map(p => p.y));
    const maxY = Math.max(...positions.map(p => p.y));

    console.log('居中前边界框:', { minX, maxX, minY, maxY });

    // 计算布局的中心点
    const layoutCenterX = (minX + maxX) / 2;
    const layoutCenterY = (minY + maxY) / 2;

    console.log('布局中心点:', { layoutCenterX, layoutCenterY });

    // 将布局中心移动到坐标原点(0, 0)，并应用边距
    const centeredNodes = nodes.map(node => ({
        ...node,
        position: {
            x: node.position.x - layoutCenterX,
            y: node.position.y - layoutCenterY
        }
    }));

    console.log('居中后节点位置:', centeredNodes.map(n => ({ id: n.id, position: n.position })));
    return centeredNodes;
}

/**
 * 计算节点边界框
 */
export function getNodesBounds(nodes: CustomNodeType[]): { x: number; y: number; width: number; height: number } {
    if (nodes.length === 0) {
        return { x: 0, y: 0, width: 0, height: 0 };
    }

    const positions = nodes.map(n => n.position);
    const minX = Math.min(...positions.map(p => p.x));
    const maxX = Math.max(...positions.map(p => p.x));
    const minY = Math.min(...positions.map(p => p.y));
    const maxY = Math.max(...positions.map(p => p.y));

    // 假设每个节点的大小为 200x100
    const nodeWidth = 200;
    const nodeHeight = 100;

    return {
        x: minX - nodeWidth / 2,
        y: minY - nodeHeight / 2,
        width: maxX - minX + nodeWidth,
        height: maxY - minY + nodeHeight
    };
}

/**
 * 获取适合视图的缩放级别和位置
 */
export function getFitViewParams(
    nodes: CustomNodeType[],
    viewportWidth: number,
    viewportHeight: number,
    padding: number = 50
): { x: number; y: number; zoom: number } {
    if (nodes.length === 0) {
        return { x: 0, y: 0, zoom: 1 };
    }

    const bounds = getNodesBounds(nodes);

    // 计算缩放比例
    const scaleX = (viewportWidth - padding * 2) / bounds.width;
    const scaleY = (viewportHeight - padding * 2) / bounds.height;
    const zoom = Math.min(scaleX, scaleY, 1.5); // 限制最大缩放

    // 计算居中位置
    const x = viewportWidth / 2 - (bounds.x + bounds.width / 2) * zoom;
    const y = viewportHeight / 2 - (bounds.y + bounds.height / 2) * zoom;

    return { x, y, zoom };
}

/**
 * 动画过渡函数 - 用于平滑的布局变化
 */
export function animateLayout(
    fromNodes: CustomNodeType[],
    toNodes: CustomNodeType[],
    duration: number = 500,
    onUpdate: (nodes: CustomNodeType[]) => void
): void {
    const startTime = Date.now();
    const nodeMap = new Map(fromNodes.map(node => [node.id, node]));

    function animate() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeProgress = 1 - Math.pow(1 - progress, 3);

        const animatedNodes = toNodes.map(toNode => {
            const fromNode = nodeMap.get(toNode.id);
            if (!fromNode) return toNode;

            return {
                ...toNode,
                position: {
                    x: fromNode.position.x + (toNode.position.x - fromNode.position.x) * easeProgress,
                    y: fromNode.position.y + (toNode.position.y - fromNode.position.y) * easeProgress
                }
            };
        });

        onUpdate(animatedNodes);

        if (progress < 1) {
            requestAnimationFrame(animate);
        }
    }

    animate();
}
